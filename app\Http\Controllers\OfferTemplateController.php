<?php

namespace App\Http\Controllers;

use App\Models\OfferTemplate;
use App\Models\Offer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OfferTemplateController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $shopkeeper = $user->shopkeeper;

        if (!$shopkeeper) {
            return response()->json(['error' => 'Shopkeeper profile not found'], 404);
        }

        $templates = $shopkeeper->offerTemplates()
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($templates);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'template_name' => 'required|string|max:255',
            'business_name' => 'required|string|max:255',
            'offer_name' => 'required|string|max:255',
            'products_services' => 'required|string',
            'discount_type' => 'required|in:percentage,fixed',
            'discount_value' => 'required|numeric|min:0',
            'min_amount' => 'nullable|numeric|min:0',
            'currency' => 'string|size:3',
            'terms_conditions' => 'nullable|string',
            'max_uses' => 'nullable|integer|min:1',
            'radius_km' => 'integer|min:1|max:50',
        ]);

        $user = Auth::user();
        $shopkeeper = $user->shopkeeper;

        if (!$shopkeeper) {
            return response()->json(['error' => 'Shopkeeper profile not found'], 404);
        }

        $validated['shopkeeper_id'] = $shopkeeper->id;
        $validated['currency'] = $validated['currency'] ?? 'INR';
        $validated['radius_km'] = $validated['radius_km'] ?? 5;

        $template = OfferTemplate::create($validated);

        return response()->json([
            'message' => 'Template created successfully',
            'template' => $template,
        ], 201);
    }

    public function show($id)
    {
        $template = OfferTemplate::findOrFail($id);
        $user = Auth::user();

        // Check ownership
        if ($template->shopkeeper->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json($template);
    }

    public function update(Request $request, $id)
    {
        $template = OfferTemplate::findOrFail($id);
        $user = Auth::user();

        // Check ownership
        if ($template->shopkeeper->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'template_name' => 'sometimes|string|max:255',
            'business_name' => 'sometimes|string|max:255',
            'offer_name' => 'sometimes|string|max:255',
            'products_services' => 'sometimes|string',
            'discount_type' => 'sometimes|in:percentage,fixed',
            'discount_value' => 'sometimes|numeric|min:0',
            'min_amount' => 'nullable|numeric|min:0',
            'terms_conditions' => 'nullable|string',
            'max_uses' => 'nullable|integer|min:1',
            'radius_km' => 'sometimes|integer|min:1|max:50',
            'is_active' => 'boolean',
        ]);

        $template->update($validated);

        return response()->json([
            'message' => 'Template updated successfully',
            'template' => $template->fresh(),
        ]);
    }

    public function destroy($id)
    {
        $template = OfferTemplate::findOrFail($id);
        $user = Auth::user();

        // Check ownership
        if ($template->shopkeeper->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $template->delete();

        return response()->json(['message' => 'Template deleted successfully']);
    }

    // Create offer from template
    public function createOffer(Request $request, $id)
    {
        $template = OfferTemplate::findOrFail($id);
        $user = Auth::user();

        // Check ownership
        if ($template->shopkeeper->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'start_date' => 'required|date|after_or_equal:now',
            'end_date' => 'required|date|after:start_date',
            'send_notifications' => 'boolean',
            // Allow overriding template values
            'business_name' => 'sometimes|string|max:255',
            'offer_name' => 'sometimes|string|max:255',
            'discount_value' => 'sometimes|numeric|min:0',
            'min_amount' => 'nullable|numeric|min:0',
        ]);

        // Check subscription limits
        if (!$user->hasActiveSubscription() && $template->shopkeeper->offers()->count() >= 1) {
            return response()->json(['error' => 'Subscription required for multiple offers'], 403);
        }

        // Convert template to offer data
        $offerData = $template->toOfferData([
            'start_date' => $validated['start_date'],
            'end_date' => $validated['end_date'],
            'latitude' => $template->shopkeeper->latitude,
            'longitude' => $template->shopkeeper->longitude,
            'is_active' => true,
        ]);

        // Override with any provided values
        $offerData = array_merge($offerData, array_intersect_key($validated, [
            'business_name' => '',
            'offer_name' => '',
            'discount_value' => '',
            'min_amount' => '',
        ]));

        $offer = Offer::create($offerData);

        // Send notifications if requested
        if ($request->send_notifications) {
            $notificationCount = \App\Models\Notification::sendToNearbyCustomers(
                $offer->id,
                $template->shopkeeper->latitude,
                $template->shopkeeper->longitude,
                $offer->radius_km
            );
            
            $offer->notification_count = $notificationCount;
        }

        return response()->json([
            'message' => 'Offer created from template successfully',
            'offer' => $offer->load('shopkeeper.user'),
        ], 201);
    }
}
