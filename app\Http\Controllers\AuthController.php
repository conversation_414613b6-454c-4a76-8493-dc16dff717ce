<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use App\Mail\OtpMail;
use Illuminate\Support\Facades\Auth;

class AuthController extends Controller
{
    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'email' => 'required|string|email|unique:users',
            'password' => 'required|string|min:6',
            'role' => 'required|in:super-admin,shop-keeper,customer',
        ]);

        $otp = rand(100000, 999999);
        $otpExpiry = Carbon::now()->addMinutes(10);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'otp' => $otp,
            'otp_expires_at' => $otpExpiry,
        ]);

        // Send OTP via email
        Mail::to($user->email)->send(new OtpMail($otp));

        return response()->json([
            'message' => 'OTP sent to your email',
            'user_id' => $user->id,
            'registration_step' => 'email_verification'
        ]);
    }

    // Verify OTP
    public function verifyOtp(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'otp' => 'required|digits:6',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        if ($user->otp !== $request->otp || Carbon::now()->greaterThan($user->otp_expires_at)) {
            return response()->json(['error' => 'Invalid or expired OTP'], 400);
        }

        // Mark as verified
        $user->is_verified = true;
        $user->otp = null;
        $user->otp_expires_at = null;
        $user->registration_step = 'phone_verification';
        $user->save();

        return response()->json([
            'message' => 'Email verified successfully',
            'registration_step' => 'phone_verification',
            'next_step' => 'Please verify your phone number'
        ]);
    }

    // Login with Email and OTP
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json(['error' => 'Invalid credentials'], 401);
        }

        if (!$user->is_verified) {
            return response()->json(['error' => 'Email not verified. Please verify using OTP.'], 403);
        }

        $token = $user->createToken('api-token')->plainTextToken;

        return response()->json(['token' => $token, 'user' => $user]);
    }

    // Get User Profile
    public function getProfile(Request $request)
    {
        $user = Auth::user();
        return response()->json(['user' => $user], 200);
    }

    // Send phone OTP
    public function sendPhoneOtp(Request $request)
    {
        $request->validate([
            'phone' => 'required|string|min:10|max:15',
            'email' => 'required|email',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        if (!$user->is_verified) {
            return response()->json(['error' => 'Email not verified'], 403);
        }

        $phoneOtp = rand(100000, 999999);
        $phoneOtpExpiry = Carbon::now()->addMinutes(10);

        $user->update([
            'phone' => $request->phone,
            'phone_otp' => $phoneOtp,
            'phone_otp_expires_at' => $phoneOtpExpiry,
        ]);

        // Here you would integrate with SMS service to send OTP
        // For now, we'll just return the OTP in response (remove in production)
        return response()->json([
            'message' => 'Phone OTP sent successfully',
            'phone_otp' => $phoneOtp, // Remove this in production
            'registration_step' => 'phone_verification'
        ]);
    }

    // Verify phone OTP
    public function verifyPhoneOtp(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'phone_otp' => 'required|digits:6',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        if ($user->phone_otp !== $request->phone_otp || Carbon::now()->greaterThan($user->phone_otp_expires_at)) {
            return response()->json(['error' => 'Invalid or expired phone OTP'], 400);
        }

        $user->update([
            'phone_verified' => true,
            'phone_otp' => null,
            'phone_otp_expires_at' => null,
            'registration_step' => 'profile_setup',
        ]);

        return response()->json([
            'message' => 'Phone verified successfully',
            'registration_step' => 'profile_setup',
            'next_step' => 'Please complete your profile'
        ]);
    }

    // Resend phone OTP
    public function resendPhoneOtp(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        if (!$user->phone) {
            return response()->json(['error' => 'Phone number not provided'], 400);
        }

        $phoneOtp = rand(100000, 999999);
        $phoneOtpExpiry = Carbon::now()->addMinutes(10);

        $user->update([
            'phone_otp' => $phoneOtp,
            'phone_otp_expires_at' => $phoneOtpExpiry,
        ]);

        // Here you would integrate with SMS service to send OTP
        return response()->json([
            'message' => 'Phone OTP resent successfully',
            'phone_otp' => $phoneOtp, // Remove this in production
        ]);
    }

    // Logout
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        return response()->json(['message' => 'Logged out']);
    }
}
