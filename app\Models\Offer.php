<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Offer extends Model
{
    protected $fillable = [
        'shopkeeper_id',
        'business_name',
        'offer_name',
        'products_services',
        'discount_type',
        'discount_value',
        'min_amount',
        'currency',
        'start_date',
        'end_date',
        'is_active',
        'is_template',
        'terms_conditions',
        'max_uses',
        'used_count',
        'latitude',
        'longitude',
        'radius_km',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
        'is_template' => 'boolean',
        'discount_value' => 'decimal:2',
        'min_amount' => 'decimal:2',
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
    ];

    public function shopkeeper(): BelongsTo
    {
        return $this->belongsTo(Shopkeeper::class);
    }

    public function views(): HasMany
    {
        return $this->hasMany(OfferView::class);
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    // Scope for active offers
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    // Scope for nearby offers based on location
    public function scopeNearby($query, $latitude, $longitude, $radiusKm = 10)
    {
        return $query->selectRaw("
            *, 
            (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
        ", [$latitude, $longitude, $latitude])
        ->having('distance', '<=', $radiusKm)
        ->orderBy('distance');
    }

    // Check if offer is currently valid
    public function isValid(): bool
    {
        return $this->is_active && 
               $this->start_date <= now() && 
               $this->end_date >= now() &&
               ($this->max_uses === null || $this->used_count < $this->max_uses);
    }

    // Get formatted discount text
    public function getDiscountTextAttribute(): string
    {
        if ($this->discount_type === 'percentage') {
            return $this->discount_value . '% off';
        }
        return $this->currency . ' ' . $this->discount_value . ' off';
    }

    // Get formatted offer description
    public function getFormattedDescriptionAttribute(): string
    {
        $description = $this->offer_name . ' - ' . $this->discount_text;
        if ($this->min_amount) {
            $description .= ' on min bill value ' . $this->currency . ' ' . $this->min_amount;
        }
        return $description;
    }
}
