<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OfferView extends Model
{
    protected $fillable = [
        'offer_id',
        'customer_id',
        'action_type',
        'customer_latitude',
        'customer_longitude',
        'distance_km',
        'device_info',
        'ip_address',
    ];

    protected $casts = [
        'customer_latitude' => 'decimal:7',
        'customer_longitude' => 'decimal:7',
        'distance_km' => 'decimal:2',
    ];

    public function offer(): BelongsTo
    {
        return $this->belongsTo(Offer::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Record offer interaction
    public static function recordInteraction($offerId, $customerId, $actionType, $additionalData = []): self
    {
        return self::updateOrCreate(
            [
                'offer_id' => $offerId,
                'customer_id' => $customerId,
                'action_type' => $actionType,
            ],
            array_merge([
                'customer_latitude' => $additionalData['latitude'] ?? null,
                'customer_longitude' => $additionalData['longitude'] ?? null,
                'distance_km' => $additionalData['distance_km'] ?? null,
                'device_info' => $additionalData['device_info'] ?? null,
                'ip_address' => $additionalData['ip_address'] ?? request()->ip(),
            ], $additionalData)
        );
    }

    // Get analytics for offer
    public static function getOfferAnalytics($offerId): array
    {
        $views = self::where('offer_id', $offerId);
        
        return [
            'total_views' => $views->where('action_type', 'view')->count(),
            'total_clicks' => $views->where('action_type', 'click')->count(),
            'total_shares' => $views->where('action_type', 'share')->count(),
            'total_redeems' => $views->where('action_type', 'redeem')->count(),
            'unique_customers' => $views->distinct('customer_id')->count(),
            'average_distance' => $views->avg('distance_km'),
        ];
    }
}
