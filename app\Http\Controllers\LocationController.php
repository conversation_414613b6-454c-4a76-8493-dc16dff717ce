<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Shopkeeper;
use App\Models\Offer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LocationController extends Controller
{
    // Get nearby customers for shopkeepers
    public function nearbyCustomers(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'radius' => 'integer|min:1|max:50',
        ]);

        $user = Auth::user();
        if ($user->role !== 'shop-keeper') {
            return response()->json(['error' => 'Only shopkeepers can view nearby customers'], 403);
        }

        $radius = $request->radius ?? 10;

        $customers = Customer::selectRaw("
            customer_details.*, 
            users.name,
            users.email,
            (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
        ", [$request->latitude, $request->longitude, $request->latitude])
        ->join('users', 'customer_details.user_id', '=', 'users.id')
        ->having('distance', '<=', $radius)
        ->orderBy('distance')
        ->paginate(50);

        // Add statistics
        $stats = [
            'total_customers' => $customers->total(),
            'radius_km' => $radius,
            'center_latitude' => $request->latitude,
            'center_longitude' => $request->longitude,
        ];

        return response()->json([
            'customers' => $customers,
            'stats' => $stats,
        ]);
    }

    // Get nearby shopkeepers for customers
    public function nearbyShopkeepers(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'radius' => 'integer|min:1|max:50',
        ]);

        $user = Auth::user();
        if ($user->role !== 'customer') {
            return response()->json(['error' => 'Only customers can view nearby shopkeepers'], 403);
        }

        $radius = $request->radius ?? 10;

        $shopkeepers = Shopkeeper::selectRaw("
            shopkeeper_details.*, 
            users.name,
            users.email,
            (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
        ", [$request->latitude, $request->longitude, $request->latitude])
        ->join('users', 'shopkeeper_details.user_id', '=', 'users.id')
        ->having('distance', '<=', $radius)
        ->orderBy('distance')
        ->with(['activeOffers' => function($query) {
            $query->limit(3); // Show only 3 latest active offers per shopkeeper
        }])
        ->paginate(20);

        return response()->json($shopkeepers);
    }

    // Get nearby offers for customers
    public function nearbyOffers(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'radius' => 'integer|min:1|max:50',
            'category' => 'string|max:255',
        ]);

        $user = Auth::user();
        if ($user->role !== 'customer') {
            return response()->json(['error' => 'Only customers can view nearby offers'], 403);
        }

        $radius = $request->radius ?? 10;

        $offers = Offer::active()
            ->nearby($request->latitude, $request->longitude, $radius)
            ->with(['shopkeeper.user'])
            ->when($request->category, function ($query, $category) {
                return $query->where('products_services', 'like', "%{$category}%");
            })
            ->paginate(20);

        // Add map visualization data
        $mapData = [
            'center' => [
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
            ],
            'radius_km' => $radius,
            'offers_count' => $offers->total(),
        ];

        return response()->json([
            'offers' => $offers,
            'map_data' => $mapData,
        ]);
    }

    // Get location statistics for dashboard
    public function statistics(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'radius' => 'integer|min:1|max:50',
        ]);

        $user = Auth::user();
        $radius = $request->radius ?? 10;

        $stats = [];

        if ($user->role === 'shop-keeper') {
            // Statistics for shopkeepers
            $nearbyCustomersCount = Customer::selectRaw("
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
            ", [$request->latitude, $request->longitude, $request->latitude])
            ->having('distance', '<=', $radius)
            ->count();

            $stats = [
                'nearby_customers' => $nearbyCustomersCount,
                'active_offers' => $user->shopkeeper ? $user->shopkeeper->activeOffers()->count() : 0,
                'total_offers' => $user->shopkeeper ? $user->shopkeeper->offers()->count() : 0,
                'radius_km' => $radius,
            ];

        } elseif ($user->role === 'customer') {
            // Statistics for customers
            $nearbyOffersCount = Offer::active()
                ->nearby($request->latitude, $request->longitude, $radius)
                ->count();

            $nearbyShopkeepersCount = Shopkeeper::selectRaw("
                (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
            ", [$request->latitude, $request->longitude, $request->latitude])
            ->having('distance', '<=', $radius)
            ->count();

            $stats = [
                'nearby_offers' => $nearbyOffersCount,
                'nearby_shopkeepers' => $nearbyShopkeepersCount,
                'radius_km' => $radius,
            ];
        }

        return response()->json($stats);
    }

    // Update user location
    public function updateLocation(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        $user = Auth::user();

        if ($user->role === 'customer' && $user->customer) {
            $user->customer->update([
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
            ]);
        } elseif ($user->role === 'shop-keeper' && $user->shopkeeper) {
            $user->shopkeeper->update([
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
            ]);
        } else {
            return response()->json(['error' => 'User profile not found'], 404);
        }

        return response()->json(['message' => 'Location updated successfully']);
    }
}
