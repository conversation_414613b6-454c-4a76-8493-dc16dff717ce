<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->string('phone_otp')->nullable()->after('otp_expires_at');
            $table->timestamp('phone_otp_expires_at')->nullable()->after('phone_otp');
            $table->boolean('phone_verified')->default(false)->after('phone_otp_expires_at');
            $table->string('device_token')->nullable()->after('phone_verified'); // For push notifications
            $table->enum('registration_step', ['email_verification', 'phone_verification', 'profile_setup', 'completed'])->default('email_verification')->after('device_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone', 
                'phone_otp', 
                'phone_otp_expires_at', 
                'phone_verified', 
                'device_token',
                'registration_step'
            ]);
        });
    }
};
