[2025-03-27 10:59:32] local.ERROR: Call to undefined method App\Models\User::createToken() {"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\User::createToken() at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:67)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException('createToken')
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2368): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'createToken', Array)
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\app\\Http\\Controllers\\AuthController.php(87): Illuminate\\Database\\Eloquent\\Model->__call('createToken', Array)
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\AuthController->login(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AuthController), 'login')
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#11 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():198}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1193): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#39 {main}
"} 
[2025-04-24 08:32:47] local.ERROR: The use statement with non-compound name 'Log' has no effect {"exception":"[object] (ErrorException(code: 0): The use statement with non-compound name 'Log' has no effect at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\routes\\api.php:6)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(290): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'The use stateme...', 'C:\\\\Users\\\\<USER>\\\\D...', 6)
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():289}(2, 'The use stateme...', 'C:\\\\Users\\\\<USER>\\\\D...', 6)
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('C:\\\\Users\\\\<USER>\\\\D...')
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(513): Illuminate\\Routing\\RouteFileRegistrar->register('C:\\\\Users\\\\<USER>\\\\D...')
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes('C:\\\\Users\\\\<USER>\\\\D...')
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, 'C:\\\\Users\\\\<USER>\\\\D...')
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(214): Illuminate\\Routing\\RouteRegistrar->group('C:\\\\Users\\\\<USER>\\\\D...')
#7 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->{closure:Illuminate\\Foundation\\Configuration\\ApplicationBuilder::buildRoutingCallback():205}()
#8 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#9 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#12 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#14 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->{closure:Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider::register():53}()
#15 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#16 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(83): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#19 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(144): Illuminate\\Container\\Container->call(Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1127): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#21 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1105): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider))
#22 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1104}(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider), 'Illuminate\\\\Foun...')
#23 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1104): array_walk(Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(319): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#26 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#27 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#28 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1208): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}
"} 
[2025-04-24 08:32:58] local.ERROR: Command "make:miration" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make:miration\" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('make:miration')
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1208): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-04-27 03:01:02] local.ERROR: Too few arguments to function App\Http\Middleware\RoleMiddleware::handle(), 2 passed in C:\Users\<USER>\Desktop\builds\offeraiy-api-service-one\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php on line 209 and exactly 3 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Http\\Middleware\\RoleMiddleware::handle(), 2 passed in C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php on line 209 and exactly 3 expected at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\app\\Http\\Middleware\\RoleMiddleware.php:11)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1193): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#7 {main}
"} 
[2025-04-27 03:04:47] local.ERROR: Too few arguments to function App\Http\Middleware\RoleMiddleware::handle(), 2 passed in C:\Users\<USER>\Desktop\builds\offeraiy-api-service-one\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php on line 209 and exactly 3 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Http\\Middleware\\RoleMiddleware::handle(), 2 passed in C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php on line 209 and exactly 3 expected at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\app\\Http\\Middleware\\RoleMiddleware.php:11)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1193): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#7 {main}
"} 
[2025-04-27 03:07:22] local.ERROR: Object of type App\Http\Middleware\RoleMiddleware is not callable {"exception":"[object] (Error(code: 0): Object of type App\\Http\\Middleware\\RoleMiddleware is not callable at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:210)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1193): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#6 {main}
"} 
[2025-05-01 16:20:32] local.ERROR: Too few arguments to function App\Http\Middleware\RoleMiddleware::handle(), 2 passed in C:\Users\<USER>\Desktop\builds\offeraiy-api-service-one\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php on line 209 and exactly 3 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Http\\Middleware\\RoleMiddleware::handle(), 2 passed in C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php on line 209 and exactly 3 expected at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\app\\Http\\Middleware\\RoleMiddleware.php:11)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1193): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#7 {main}
"} 
[2025-05-01 16:20:40] local.ERROR: Too few arguments to function App\Http\Middleware\RoleMiddleware::handle(), 2 passed in C:\Users\<USER>\Desktop\builds\offeraiy-api-service-one\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php on line 209 and exactly 3 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Http\\Middleware\\RoleMiddleware::handle(), 2 passed in C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php on line 209 and exactly 3 expected at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\app\\Http\\Middleware\\RoleMiddleware.php:11)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1193): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#7 {main}
"} 
[2025-05-01 16:25:53] local.ERROR: Too few arguments to function App\Http\Middleware\RoleMiddleware::handle(), 2 passed in C:\Users\<USER>\Desktop\builds\offeraiy-api-service-one\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php on line 209 and exactly 3 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Http\\Middleware\\RoleMiddleware::handle(), 2 passed in C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php on line 209 and exactly 3 expected at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\app\\Http\\Middleware\\RoleMiddleware.php:11)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1193): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#7 {main}
"} 
[2025-05-01 16:27:14] local.ERROR: Too few arguments to function App\Http\Middleware\RoleMiddleware::handle(), 2 passed in C:\Users\<USER>\Desktop\builds\offeraiy-api-service-one\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php on line 209 and exactly 3 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Http\\Middleware\\RoleMiddleware::handle(), 2 passed in C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php on line 209 and exactly 3 expected at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\app\\Http\\Middleware\\RoleMiddleware.php:11)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():184}:185}(Object(Illuminate\\Http\\Request))
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1193): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\D...')
#7 {main}
"} 
[2025-06-01 11:29:29] local.ERROR: SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation "customers" does not exist
LINE 1: insert into "customers" ("user_id", "address", "phone", "lat...
                    ^ (Connection: pgsql, SQL: insert into "customers" ("user_id", "address", "phone", "latitude", "longitude", "updated_at", "created_at") values (9, Address 1, New York, NY, +1234567001, 40.6528, -73.918, 2025-06-01 11:29:29, 2025-06-01 11:29:29) returning "id") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"customers\" does not exist
LINE 1: insert into \"customers\" (\"user_id\", \"address\", \"phone\", \"lat...
                    ^ (Connection: pgsql, SQL: insert into \"customers\" (\"user_id\", \"address\", \"phone\", \"latitude\", \"longitude\", \"updated_at\", \"created_at\") values (9, Address 1, New York, NY, +1234567001, 40.6528, -73.918, 2025-06-01 11:29:29, 2025-06-01 11:29:29) returning \"id\") at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"cu...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('insert into \"cu...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(385): Illuminate\\Database\\Connection->select('insert into \"cu...', Array, false)
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\PostgresProcessor.php(24): Illuminate\\Database\\Connection->selectFromWriteConnection('insert into \"cu...', Array)
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3799): Illuminate\\Database\\Query\\Processors\\PostgresProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"cu...', Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2047): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1081): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1080}(Object(App\\Models\\Customer))
#11 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1080): tap(Object(App\\Models\\Customer), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2368): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2380): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\database\\seeders\\OfferManagementSeeder.php(32): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\OfferManagementSeeder->run()
#17 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#18 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#22 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->{closure:Illuminate\\Database\\Seeder::__invoke():183}()
#23 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#24 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->{closure:Illuminate\\Database\\Console\\Seeds\\SeedCommand::handle():69}()
#25 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#27 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#28 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#32 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1208): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#40 {main}

[previous exception] [object] (PDOException(code: 42P01): SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"customers\" does not exist
LINE 1: insert into \"customers\" (\"user_id\", \"address\", \"phone\", \"lat...
                    ^ at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:412)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(412): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}('insert into \"cu...', Array)
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"cu...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('insert into \"cu...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(385): Illuminate\\Database\\Connection->select('insert into \"cu...', Array, false)
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\PostgresProcessor.php(24): Illuminate\\Database\\Connection->selectFromWriteConnection('insert into \"cu...', Array)
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3799): Illuminate\\Database\\Query\\Processors\\PostgresProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"cu...', Array, 'id')
#7 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2047): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1081): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1080}(Object(App\\Models\\Customer))
#13 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1080): tap(Object(App\\Models\\Customer), Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2368): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2380): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\database\\seeders\\OfferManagementSeeder.php(32): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\OfferManagementSeeder->run()
#19 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#20 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#24 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->{closure:Illuminate\\Database\\Seeder::__invoke():183}()
#25 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#26 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->{closure:Illuminate\\Database\\Console\\Seeds\\SeedCommand::handle():69}()
#27 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#29 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#30 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#33 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#34 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1208): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#42 {main}
"} 
[2025-06-01 11:30:16] local.ERROR: SQLSTATE[23505]: Unique violation: 7 ERROR:  duplicate key value violates unique constraint "users_email_unique"
DETAIL:  Key (email)=(<EMAIL>) already exists. (Connection: pgsql, SQL: insert into "users" ("name", "email", "password", "role", "is_verified", "phone_verified", "phone", "registration_step", "updated_at", "created_at") values (Customer 1, <EMAIL>, $2y$12$Sds.KQHEhmEFA09jaakIN.B1bwqVi8/IfsWkiHomTCyWWPhCxTqXi, customer, 1, 1, +1234567001, completed, 2025-06-01 11:30:16, 2025-06-01 11:30:16) returning "id") {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23505): SQLSTATE[23505]: Unique violation: 7 ERROR:  duplicate key value violates unique constraint \"users_email_unique\"
DETAIL:  Key (email)=(<EMAIL>) already exists. (Connection: pgsql, SQL: insert into \"users\" (\"name\", \"email\", \"password\", \"role\", \"is_verified\", \"phone_verified\", \"phone\", \"registration_step\", \"updated_at\", \"created_at\") values (Customer 1, <EMAIL>, $2y$12$Sds.KQHEhmEFA09jaakIN.B1bwqVi8/IfsWkiHomTCyWWPhCxTqXi, customer, 1, 1, +1234567001, completed, 2025-06-01 11:30:16, 2025-06-01 11:30:16) returning \"id\") at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:820)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(385): Illuminate\\Database\\Connection->select('insert into \"us...', Array, false)
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\PostgresProcessor.php(24): Illuminate\\Database\\Connection->selectFromWriteConnection('insert into \"us...', Array)
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3799): Illuminate\\Database\\Query\\Processors\\PostgresProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"us...', Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2047): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1081): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1080}(Object(App\\Models\\User))
#11 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1080): tap(Object(App\\Models\\User), Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2368): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2380): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\database\\seeders\\OfferManagementSeeder.php(21): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\OfferManagementSeeder->run()
#17 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#18 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#22 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->{closure:Illuminate\\Database\\Seeder::__invoke():183}()
#23 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#24 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->{closure:Illuminate\\Database\\Console\\Seeds\\SeedCommand::handle():69}()
#25 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#27 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#28 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#32 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1208): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#40 {main}

[previous exception] [object] (PDOException(code: 23505): SQLSTATE[23505]: Unique violation: 7 ERROR:  duplicate key value violates unique constraint \"users_email_unique\"
DETAIL:  Key (email)=(<EMAIL>) already exists. at C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:412)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(412): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():398}('insert into \"us...', Array)
#2 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(385): Illuminate\\Database\\Connection->select('insert into \"us...', Array, false)
#5 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\PostgresProcessor.php(24): Illuminate\\Database\\Connection->selectFromWriteConnection('insert into \"us...', Array)
#6 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3799): Illuminate\\Database\\Query\\Processors\\PostgresProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"us...', Array, 'id')
#7 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2047): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1359): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1324): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1163): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1081): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->{closure:Illuminate\\Database\\Eloquent\\Builder::create():1080}(Object(App\\Models\\User))
#13 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1080): tap(Object(App\\Models\\User), Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2368): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2380): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\database\\seeders\\OfferManagementSeeder.php(21): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\OfferManagementSeeder->run()
#19 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#20 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#24 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->{closure:Illuminate\\Database\\Seeder::__invoke():183}()
#25 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#26 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->{closure:Illuminate\\Database\\Console\\Seeds\\SeedCommand::handle():69}()
#27 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#29 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#30 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#33 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#34 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1208): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Desktop\\builds\\offeraiy-api-service-one\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#42 {main}
"} 
