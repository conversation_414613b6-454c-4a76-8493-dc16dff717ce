<?php

namespace App\Http\Controllers;

use App\Models\Offer;
use App\Models\OfferView;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class OfferController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $shopkeeper = $user->shopkeeper;

        if (!$shopkeeper) {
            return response()->json(['error' => 'Shopkeeper profile not found'], 404);
        }

        $offers = $shopkeeper->offers()
            ->when($request->status, function ($query, $status) {
                if ($status === 'active') {
                    return $query->active();
                } elseif ($status === 'inactive') {
                    return $query->where('is_active', false);
                }
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return response()->json($offers);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'business_name' => 'required|string|max:255',
            'offer_name' => 'required|string|max:255',
            'products_services' => 'required|string',
            'discount_type' => 'required|in:percentage,fixed',
            'discount_value' => 'required|numeric|min:0',
            'min_amount' => 'nullable|numeric|min:0',
            'currency' => 'string|size:3',
            'start_date' => 'required|date|after_or_equal:now',
            'end_date' => 'required|date|after:start_date',
            'terms_conditions' => 'nullable|string',
            'max_uses' => 'nullable|integer|min:1',
            'radius_km' => 'integer|min:1|max:50',
            'is_template' => 'boolean',
            'send_notifications' => 'boolean',
        ]);

        $user = Auth::user();
        $shopkeeper = $user->shopkeeper;

        if (!$shopkeeper) {
            return response()->json(['error' => 'Shopkeeper profile not found'], 404);
        }

        // Check subscription limits
        if (!$user->hasActiveSubscription() && $shopkeeper->offers()->count() >= 1) {
            return response()->json(['error' => 'Subscription required for multiple offers'], 403);
        }

        $validated['shopkeeper_id'] = $shopkeeper->id;
        $validated['latitude'] = $shopkeeper->latitude;
        $validated['longitude'] = $shopkeeper->longitude;
        $validated['currency'] = $validated['currency'] ?? 'INR';
        $validated['radius_km'] = $validated['radius_km'] ?? 5;

        $offer = Offer::create($validated);

        // Send notifications to nearby customers if requested
        if ($request->send_notifications) {
            $notificationCount = Notification::sendToNearbyCustomers(
                $offer->id,
                $shopkeeper->latitude,
                $shopkeeper->longitude,
                $offer->radius_km
            );
            
            $offer->notification_count = $notificationCount;
        }

        return response()->json([
            'message' => 'Offer created successfully',
            'offer' => $offer->load('shopkeeper.user'),
        ], 201);
    }

    public function show($id)
    {
        $offer = Offer::with(['shopkeeper.user', 'views'])
            ->findOrFail($id);

        // Check if user can view this offer
        $user = Auth::user();
        if ($user->role === 'shop-keeper' && $offer->shopkeeper->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Record view if customer
        if ($user->role === 'customer' && $user->customer) {
            OfferView::recordInteraction($offer->id, $user->customer->id, 'view', [
                'latitude' => $user->customer->latitude,
                'longitude' => $user->customer->longitude,
                'device_info' => $request->header('User-Agent'),
            ]);
        }

        // Add analytics for shopkeeper
        if ($user->role === 'shop-keeper') {
            $offer->analytics = OfferView::getOfferAnalytics($offer->id);
        }

        return response()->json($offer);
    }

    public function update(Request $request, $id)
    {
        $offer = Offer::findOrFail($id);
        $user = Auth::user();

        // Check ownership
        if ($offer->shopkeeper->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'business_name' => 'sometimes|string|max:255',
            'offer_name' => 'sometimes|string|max:255',
            'products_services' => 'sometimes|string',
            'discount_type' => 'sometimes|in:percentage,fixed',
            'discount_value' => 'sometimes|numeric|min:0',
            'min_amount' => 'nullable|numeric|min:0',
            'end_date' => 'sometimes|date|after:start_date',
            'terms_conditions' => 'nullable|string',
            'max_uses' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
        ]);

        $offer->update($validated);

        return response()->json([
            'message' => 'Offer updated successfully',
            'offer' => $offer->fresh(),
        ]);
    }

    public function destroy($id)
    {
        $offer = Offer::findOrFail($id);
        $user = Auth::user();

        // Check ownership
        if ($offer->shopkeeper->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $offer->delete();

        return response()->json(['message' => 'Offer deleted successfully']);
    }

    // Get nearby offers for customers
    public function nearby(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'radius' => 'integer|min:1|max:50',
        ]);

        $user = Auth::user();
        if ($user->role !== 'customer') {
            return response()->json(['error' => 'Only customers can view nearby offers'], 403);
        }

        $radius = $request->radius ?? 10;
        
        $offers = Offer::active()
            ->nearby($request->latitude, $request->longitude, $radius)
            ->with(['shopkeeper.user'])
            ->paginate(20);

        return response()->json($offers);
    }

    // Record offer interaction
    public function interact(Request $request, $id)
    {
        $request->validate([
            'action_type' => 'required|in:click,share,redeem',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
        ]);

        $user = Auth::user();
        if ($user->role !== 'customer' || !$user->customer) {
            return response()->json(['error' => 'Only customers can interact with offers'], 403);
        }

        $offer = Offer::findOrFail($id);

        // Calculate distance if coordinates provided
        $distance = null;
        if ($request->latitude && $request->longitude) {
            $distance = $this->calculateDistance(
                $request->latitude,
                $request->longitude,
                $offer->latitude,
                $offer->longitude
            );
        }

        OfferView::recordInteraction($offer->id, $user->customer->id, $request->action_type, [
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'distance_km' => $distance,
            'device_info' => $request->header('User-Agent'),
        ]);

        // Increment used count for redeem action
        if ($request->action_type === 'redeem') {
            $offer->increment('used_count');
        }

        return response()->json(['message' => 'Interaction recorded successfully']);
    }

    // Get offer analytics
    public function analytics($id)
    {
        $offer = Offer::findOrFail($id);
        $user = Auth::user();

        // Check ownership
        if ($offer->shopkeeper->user_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $analytics = OfferView::getOfferAnalytics($offer->id);
        
        // Add time-based analytics
        $analytics['daily_views'] = OfferView::where('offer_id', $offer->id)
            ->where('action_type', 'view')
            ->where('created_at', '>=', now()->subDays(7))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json($analytics);
    }

    private function calculateDistance($lat1, $lon1, $lat2, $lon2)
    {
        $earthRadius = 6371; // km

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }
}
