<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Shopkeeper;
use App\Models\Customer;
use App\Models\Offer;
use App\Models\Subscription;
use Lara<PERSON>\Sanctum\Sanctum;

class OfferManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function user_can_register_with_email_verification()
    {
        $response = $this->postJson('/api/register', [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'shop-keeper'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'user_id',
                    'registration_step'
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'shop-keeper'
        ]);
    }

    /** @test */
    public function shopkeeper_can_complete_wizard_registration()
    {
        // Create and verify user
        $user = User::factory()->create([
            'role' => 'shop-keeper',
            'is_verified' => true,
            'phone_verified' => true,
            'registration_step' => 'profile_setup'
        ]);

        Sanctum::actingAs($user);

        $response = $this->postJson('/api/shopkeeper/details', [
            'shop_name' => 'John\'s Electronics',
            'business_license' => 'BL123456',
            'latitude' => 40.7128,
            'longitude' => -74.0060,
            'address' => '123 Main St',
            'business_type' => 'Electronics'
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'shopkeeper',
                    'registration_step'
                ]);

        $this->assertDatabaseHas('shopkeeper_details', [
            'user_id' => $user->id,
            'shop_name' => 'John\'s Electronics'
        ]);

        // Check if free subscription was created
        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $user->id,
            'plan_type' => 'free'
        ]);
    }

    /** @test */
    public function shopkeeper_can_create_offer()
    {
        $user = User::factory()->create(['role' => 'shop-keeper']);
        $shopkeeper = Shopkeeper::factory()->create(['user_id' => $user->id]);
        
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/offers', [
            'business_name' => 'John\'s Electronics',
            'offer_name' => '20% Off All Products',
            'products_services' => 'Electronics, Gadgets',
            'discount_type' => 'percentage',
            'discount_value' => 20,
            'min_amount' => 100,
            'start_date' => now()->addDay()->toDateTimeString(),
            'end_date' => now()->addDays(30)->toDateTimeString(),
            'send_notifications' => false
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'offer' => [
                        'id',
                        'business_name',
                        'offer_name',
                        'shopkeeper'
                    ]
                ]);

        $this->assertDatabaseHas('offers', [
            'shopkeeper_id' => $shopkeeper->id,
            'offer_name' => '20% Off All Products'
        ]);
    }

    /** @test */
    public function customer_can_view_nearby_offers()
    {
        $customer = User::factory()->create(['role' => 'customer']);
        $customerDetails = Customer::factory()->create([
            'user_id' => $customer->id,
            'latitude' => 40.7128,
            'longitude' => -74.0060
        ]);

        $shopkeeper = User::factory()->create(['role' => 'shop-keeper']);
        $shopkeeperDetails = Shopkeeper::factory()->create([
            'user_id' => $shopkeeper->id,
            'latitude' => 40.7130, // Very close
            'longitude' => -74.0062
        ]);

        $offer = Offer::factory()->create([
            'shopkeeper_id' => $shopkeeperDetails->id,
            'latitude' => 40.7130,
            'longitude' => -74.0062,
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDays(30)
        ]);

        Sanctum::actingAs($customer);

        $response = $this->getJson('/api/offers/nearby?latitude=40.7128&longitude=-74.0060&radius=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'offer_name',
                            'business_name',
                            'distance'
                        ]
                    ]
                ]);
    }

    /** @test */
    public function shopkeeper_can_view_nearby_customers()
    {
        $shopkeeper = User::factory()->create(['role' => 'shop-keeper']);
        $shopkeeperDetails = Shopkeeper::factory()->create([
            'user_id' => $shopkeeper->id,
            'latitude' => 40.7128,
            'longitude' => -74.0060
        ]);

        // Create nearby customers
        $customer1 = User::factory()->create(['role' => 'customer']);
        Customer::factory()->create([
            'user_id' => $customer1->id,
            'latitude' => 40.7130,
            'longitude' => -74.0062
        ]);

        $customer2 = User::factory()->create(['role' => 'customer']);
        Customer::factory()->create([
            'user_id' => $customer2->id,
            'latitude' => 40.7125,
            'longitude' => -74.0058
        ]);

        Sanctum::actingAs($shopkeeper);

        $response = $this->getJson('/api/location/nearby-customers?latitude=40.7128&longitude=-74.0060&radius=5');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'customers' => [
                        'data' => [
                            '*' => [
                                'id',
                                'name',
                                'distance'
                            ]
                        ]
                    ],
                    'stats'
                ]);
    }

    /** @test */
    public function user_can_subscribe_to_plan()
    {
        $user = User::factory()->create(['role' => 'shop-keeper']);
        
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/subscription/subscribe', [
            'plan_type' => 'free'
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'message',
                    'subscription'
                ]);

        $this->assertDatabaseHas('subscriptions', [
            'user_id' => $user->id,
            'plan_type' => 'free',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function shopkeeper_can_create_and_use_template()
    {
        $user = User::factory()->create(['role' => 'shop-keeper']);
        $shopkeeper = Shopkeeper::factory()->create(['user_id' => $user->id]);
        
        Sanctum::actingAs($user);

        // Create template
        $templateResponse = $this->postJson('/api/offer-templates', [
            'template_name' => 'Standard 20% Off',
            'business_name' => 'John\'s Electronics',
            'offer_name' => '20% Off All Products',
            'products_services' => 'Electronics',
            'discount_type' => 'percentage',
            'discount_value' => 20,
            'min_amount' => 100
        ]);

        $templateResponse->assertStatus(201);
        $templateId = $templateResponse->json('template.id');

        // Create offer from template
        $offerResponse = $this->postJson("/api/offer-templates/{$templateId}/create-offer", [
            'start_date' => now()->addDay()->toDateTimeString(),
            'end_date' => now()->addDays(30)->toDateTimeString(),
            'send_notifications' => false
        ]);

        $offerResponse->assertStatus(201)
                     ->assertJsonStructure([
                         'message',
                         'offer'
                     ]);

        $this->assertDatabaseHas('offers', [
            'shopkeeper_id' => $shopkeeper->id,
            'offer_name' => '20% Off All Products'
        ]);
    }
}
