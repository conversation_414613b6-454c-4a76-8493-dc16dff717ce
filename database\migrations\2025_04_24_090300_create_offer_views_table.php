<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offer_views', function (Blueprint $table) {
            $table->id();
            $table->foreignId('offer_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained('customer_details')->onDelete('cascade');
            $table->enum('action_type', ['view', 'click', 'share', 'redeem']);
            $table->decimal('customer_latitude', 10, 7)->nullable();
            $table->decimal('customer_longitude', 10, 7)->nullable();
            $table->decimal('distance_km', 8, 2)->nullable();
            $table->string('device_info')->nullable();
            $table->string('ip_address')->nullable();
            $table->timestamps();
            
            $table->index(['offer_id', 'action_type']);
            $table->index(['customer_id', 'created_at']);
            $table->unique(['offer_id', 'customer_id', 'action_type'], 'unique_offer_customer_action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offer_views');
    }
};
