<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    protected $table = 'customer_details';

    protected $fillable = [
        'user_id',
        'address',
        'phone',
        'latitude',
        'longitude',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function offerViews()
    {
        return $this->hasMany(OfferView::class);
    }

    // Get nearby offers
    public function nearbyOffers($radiusKm = 10)
    {
        return Offer::active()->nearby($this->latitude, $this->longitude, $radiusKm);
    }

    // Get nearby shopkeepers
    public function nearbyShopkeepers($radiusKm = 10)
    {
        return Shopkeeper::selectRaw("
            *,
            (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
        ", [$this->latitude, $this->longitude, $this->latitude])
            ->having('distance', '<=', $radiusKm)
            ->orderBy('distance');
    }
}
