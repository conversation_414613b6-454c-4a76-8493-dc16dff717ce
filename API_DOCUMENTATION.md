# OfferAI API Service Documentation

## Overview
This API service implements a location-based offer management system with wizard-based shopkeeper registration, phone verification, subscription management, and real-time notifications.

## Features Implemented

### 1. **User Authentication & Registration**
- Email-based registration with OTP verification
- Phone number verification with OTP
- Wizard-based registration flow
- Role-based access (customer, shop-keeper, super-admin)

### 2. **Location-Based Services**
- Nearby customer discovery for shopkeepers
- Nearby offer discovery for customers
- Geolocation-based filtering with customizable radius
- Distance calculations using Haversine formula

### 3. **Offer Management**
- Create, read, update, delete offers
- Offer templates for reusability
- Location-based offer visibility
- Offer analytics and tracking
- Social sharing capabilities

### 4. **Subscription Management**
- Multiple subscription plans (Free, Monthly tiers)
- Automatic subscription management
- Payment integration ready
- Feature-based access control

### 5. **Notification System**
- Push notifications for nearby offers
- Real-time notification management
- Notification analytics

## API Endpoints

### Authentication Endpoints

#### Register User
```
POST /api/register
Content-Type: application/json

{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "shop-keeper"
}
```

#### Verify Email OTP
```
POST /api/verify-otp
Content-Type: application/json

{
    "email": "<EMAIL>",
    "otp": "123456"
}
```

#### Send Phone OTP
```
POST /api/send-phone-otp
Content-Type: application/json

{
    "email": "<EMAIL>",
    "phone": "+**********"
}
```

#### Verify Phone OTP
```
POST /api/verify-phone-otp
Content-Type: application/json

{
    "email": "<EMAIL>",
    "phone_otp": "123456"
}
```

#### Login
```
POST /api/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123"
}
```

### Shopkeeper Endpoints

#### Create Shopkeeper Profile (Wizard Step)
```
POST /api/shopkeeper/details
Authorization: Bearer {token}
Content-Type: application/json

{
    "shop_name": "John's Electronics",
    "business_license": "BL123456",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "address": "123 Main St, New York",
    "phone": "+**********",
    "business_type": "Electronics",
    "description": "Best electronics store in town"
}
```

#### Get Nearby Customers
```
GET /api/location/nearby-customers?latitude=40.7128&longitude=-74.0060&radius=10
Authorization: Bearer {token}
```

### Offer Management

#### Create Offer
```
POST /api/offers
Authorization: Bearer {token}
Content-Type: application/json

{
    "business_name": "John's Electronics",
    "offer_name": "20% Off All Products",
    "products_services": "Electronics, Gadgets, Accessories",
    "discount_type": "percentage",
    "discount_value": 20,
    "min_amount": 100,
    "start_date": "2025-01-01 00:00:00",
    "end_date": "2025-01-31 23:59:59",
    "terms_conditions": "Valid on minimum purchase of $100",
    "max_uses": 100,
    "radius_km": 5,
    "send_notifications": true
}
```

#### Get Nearby Offers (Customer)
```
GET /api/offers/nearby?latitude=40.7128&longitude=-74.0060&radius=10
Authorization: Bearer {token}
```

#### Get Offer Analytics
```
GET /api/offers/{id}/analytics
Authorization: Bearer {token}
```

### Offer Templates

#### Create Template
```
POST /api/offer-templates
Authorization: Bearer {token}
Content-Type: application/json

{
    "template_name": "Standard 20% Off",
    "business_name": "John's Electronics",
    "offer_name": "20% Off All Products",
    "products_services": "Electronics",
    "discount_type": "percentage",
    "discount_value": 20,
    "min_amount": 100
}
```

#### Create Offer from Template
```
POST /api/offer-templates/{id}/create-offer
Authorization: Bearer {token}
Content-Type: application/json

{
    "start_date": "2025-01-01 00:00:00",
    "end_date": "2025-01-31 23:59:59",
    "send_notifications": true
}
```

### Subscription Management

#### Get Available Plans
```
GET /api/subscription/plans
```

#### Subscribe to Plan
```
POST /api/subscription/subscribe
Authorization: Bearer {token}
Content-Type: application/json

{
    "plan_type": "monthly_59",
    "payment_method": "credit_card",
    "transaction_id": "txn_123456"
}
```

#### Get Current Subscription
```
GET /api/subscription/current
Authorization: Bearer {token}
```

### Location Services

#### Update Location
```
POST /api/location/update
Authorization: Bearer {token}
Content-Type: application/json

{
    "latitude": 40.7128,
    "longitude": -74.0060
}
```

#### Get Location Statistics
```
GET /api/location/statistics?latitude=40.7128&longitude=-74.0060&radius=10
Authorization: Bearer {token}
```

### Notifications

#### Get Notifications
```
GET /api/notifications?type=offer&unread_only=true
Authorization: Bearer {token}
```

#### Mark Notification as Read
```
POST /api/notifications/{id}/mark-read
Authorization: Bearer {token}
```

## Database Schema

### Key Tables
1. **users** - User authentication and basic info
2. **customer_details** - Customer profile with location
3. **shopkeeper_details** - Shopkeeper profile with business info
4. **offers** - Offer details with location and timing
5. **offer_templates** - Reusable offer templates
6. **subscriptions** - User subscription management
7. **offer_views** - Offer interaction tracking
8. **notifications** - Push notification management

## Installation & Setup

1. **Install Dependencies**
```bash
composer install
```

2. **Run Migrations**
```bash
php artisan migrate
```

3. **Start Server**
```bash
php artisan serve
```

## Key Features Matching Screenshots

### Screen 1: Customer Discovery Map
- Implemented via `/api/location/nearby-customers` endpoint
- Returns customers within specified radius with distance calculations
- Supports map visualization data

### Screen 2: Offer Creation Form
- Implemented via `/api/offers` POST endpoint
- Supports all form fields: business name, offer name, products/services, discount, minimum amount, date/time
- Validates input and creates offers with location data

### Screen 3: Offer Preview & Sharing
- Offer data includes formatted descriptions
- Social sharing metadata included in responses
- Agreement tracking via offer interactions

### Screen 4: Offer Templates
- Full CRUD operations via `/api/offer-templates`
- Template-to-offer conversion functionality
- Reusable template management

### Screen 5: Subscription Plans
- Multiple plan tiers with different features
- Free trial and paid monthly options
- Automatic subscription management

### Screen 6: Phone Verification
- Two-step phone verification process
- OTP generation and validation
- Resend OTP functionality

## Next Steps for Production

1. **SMS Integration** - Replace mock phone OTP with real SMS service
2. **Payment Gateway** - Integrate with Stripe/PayPal for subscriptions
3. **Push Notifications** - Implement Firebase/OneSignal for real-time notifications
4. **Email Templates** - Create branded email templates for OTP
5. **Rate Limiting** - Add API rate limiting for security
6. **Caching** - Implement Redis caching for location queries
7. **Testing** - Add comprehensive unit and integration tests
