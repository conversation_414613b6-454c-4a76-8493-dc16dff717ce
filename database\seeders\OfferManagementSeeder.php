<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Customer;
use App\Models\Shopkeeper;
use App\Models\Offer;
use App\Models\OfferTemplate;
use App\Models\Subscription;
use Illuminate\Support\Facades\Hash;

class OfferManagementSeeder extends Seeder
{
    public function run(): void
    {
        // Create sample customers
        $customers = [];
        for ($i = 1; $i <= 50; $i++) {
            $user = User::create([
                'name' => "Customer {$i}",
                'email' => "customer{$i}@example.com",
                'password' => Hash::make('password'),
                'role' => 'customer',
                'is_verified' => true,
                'phone_verified' => true,
                'phone' => "+1234567" . str_pad($i, 3, '0', STR_PAD_LEFT),
                'registration_step' => 'completed',
            ]);

            $customer = Customer::create([
                'user_id' => $user->id,
                'address' => "Address {$i}, New York, NY",
                'phone' => $user->phone,
                'latitude' => 40.7128 + (rand(-100, 100) / 1000), // Random around NYC
                'longitude' => -74.0060 + (rand(-100, 100) / 1000),
            ]);

            $customers[] = $customer;
        }

        // Create sample shopkeepers
        $shopkeepers = [];
        $businessTypes = ['Electronics', 'Clothing', 'Food & Beverage', 'Books', 'Home & Garden', 'Beauty', 'Sports', 'Automotive'];
        
        for ($i = 1; $i <= 20; $i++) {
            $user = User::create([
                'name' => "Shopkeeper {$i}",
                'email' => "shopkeeper{$i}@example.com",
                'password' => Hash::make('password'),
                'role' => 'shop-keeper',
                'is_verified' => true,
                'phone_verified' => true,
                'phone' => "+1234568" . str_pad($i, 3, '0', STR_PAD_LEFT),
                'registration_step' => 'completed',
            ]);

            $shopkeeper = Shopkeeper::create([
                'user_id' => $user->id,
                'shop_name' => "Shop {$i} - " . $businessTypes[array_rand($businessTypes)],
                'business_license' => "BL" . str_pad($i, 6, '0', STR_PAD_LEFT),
                'latitude' => 40.7128 + (rand(-200, 200) / 1000), // Random around NYC
                'longitude' => -74.0060 + (rand(-200, 200) / 1000),
                'address' => "Business Address {$i}, New York, NY",
                'phone' => $user->phone,
                'business_type' => $businessTypes[array_rand($businessTypes)],
                'description' => "This is a sample business description for shop {$i}.",
            ]);

            $shopkeepers[] = $shopkeeper;

            // Create free subscription for each shopkeeper
            Subscription::create([
                'user_id' => $user->id,
                'plan_type' => 'free',
                'plan_name' => 'Free for 12 hrs',
                'price' => 0,
                'currency' => 'INR',
                'start_date' => now(),
                'end_date' => now()->addHours(12),
                'is_active' => true,
                'status' => 'active',
                'features' => ['Basic offer creation', 'Limited reach'],
            ]);
        }

        // Create sample offers
        $offerNames = [
            '20% Off All Products',
            'Buy One Get One Free',
            'Flash Sale - 50% Off',
            'Weekend Special Discount',
            'New Customer Offer - 30% Off',
            'Clearance Sale - Up to 60% Off',
            'Happy Hour Special',
            'Student Discount - 15% Off',
            'Senior Citizen Special',
            'Bulk Purchase Discount'
        ];

        $productsServices = [
            'Electronics, Gadgets, Accessories',
            'Clothing, Fashion, Apparel',
            'Food, Beverages, Snacks',
            'Books, Stationery, Office Supplies',
            'Home, Garden, Furniture',
            'Beauty, Cosmetics, Personal Care',
            'Sports, Fitness, Outdoor',
            'Automotive, Parts, Accessories'
        ];

        foreach ($shopkeepers as $shopkeeper) {
            // Create 2-3 offers per shopkeeper
            $offerCount = rand(2, 3);
            for ($j = 0; $j < $offerCount; $j++) {
                $startDate = now()->addDays(rand(0, 7));
                $endDate = $startDate->copy()->addDays(rand(7, 30));

                Offer::create([
                    'shopkeeper_id' => $shopkeeper->id,
                    'business_name' => $shopkeeper->shop_name,
                    'offer_name' => $offerNames[array_rand($offerNames)],
                    'products_services' => $productsServices[array_rand($productsServices)],
                    'discount_type' => rand(0, 1) ? 'percentage' : 'fixed',
                    'discount_value' => rand(10, 50),
                    'min_amount' => rand(100, 1000),
                    'currency' => 'INR',
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'is_active' => true,
                    'terms_conditions' => 'Valid on minimum purchase. Cannot be combined with other offers.',
                    'max_uses' => rand(50, 200),
                    'used_count' => rand(0, 10),
                    'latitude' => $shopkeeper->latitude,
                    'longitude' => $shopkeeper->longitude,
                    'radius_km' => rand(5, 15),
                ]);
            }

            // Create 1-2 templates per shopkeeper
            $templateCount = rand(1, 2);
            for ($k = 0; $k < $templateCount; $k++) {
                OfferTemplate::create([
                    'shopkeeper_id' => $shopkeeper->id,
                    'template_name' => "Template " . ($k + 1) . " - " . $shopkeeper->shop_name,
                    'business_name' => $shopkeeper->shop_name,
                    'offer_name' => $offerNames[array_rand($offerNames)],
                    'products_services' => $productsServices[array_rand($productsServices)],
                    'discount_type' => rand(0, 1) ? 'percentage' : 'fixed',
                    'discount_value' => rand(10, 40),
                    'min_amount' => rand(100, 500),
                    'currency' => 'INR',
                    'terms_conditions' => 'Standard terms and conditions apply.',
                    'max_uses' => rand(100, 300),
                    'radius_km' => rand(5, 10),
                ]);
            }
        }

        // Create some paid subscriptions
        $paidPlans = ['monthly_59', 'monthly_122', 'monthly_167'];
        $selectedShopkeepers = array_slice($shopkeepers, 0, 5); // First 5 shopkeepers get paid plans

        foreach ($selectedShopkeepers as $index => $shopkeeper) {
            $planType = $paidPlans[array_rand($paidPlans)];
            $planDetails = Subscription::getPlanDetails($planType);

            // Cancel free subscription
            Subscription::where('user_id', $shopkeeper->user_id)
                      ->where('plan_type', 'free')
                      ->update(['status' => 'cancelled', 'is_active' => false]);

            // Create paid subscription
            Subscription::create([
                'user_id' => $shopkeeper->user_id,
                'plan_type' => $planType,
                'plan_name' => $planDetails['name'],
                'price' => $planDetails['price'],
                'currency' => 'INR',
                'start_date' => now(),
                'end_date' => now()->addDays(30),
                'is_active' => true,
                'status' => 'active',
                'payment_method' => 'credit_card',
                'transaction_id' => 'txn_' . uniqid(),
                'features' => $planDetails['features'],
            ]);
        }

        $this->command->info('Sample data created successfully!');
        $this->command->info('- 50 Customers created');
        $this->command->info('- 20 Shopkeepers created');
        $this->command->info('- ' . Offer::count() . ' Offers created');
        $this->command->info('- ' . OfferTemplate::count() . ' Templates created');
        $this->command->info('- ' . Subscription::count() . ' Subscriptions created');
    }
}
