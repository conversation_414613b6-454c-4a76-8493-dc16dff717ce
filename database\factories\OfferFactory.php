<?php

namespace Database\Factories;

use App\Models\Offer;
use App\Models\Shopkeeper;
use Illuminate\Database\Eloquent\Factories\Factory;

class OfferFactory extends Factory
{
    protected $model = Offer::class;

    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('now', '+1 week');
        $endDate = $this->faker->dateTimeBetween($startDate, '+1 month');

        return [
            'shopkeeper_id' => Shopkeeper::factory(),
            'business_name' => $this->faker->company,
            'offer_name' => $this->faker->randomElement([
                '20% Off All Products',
                'Buy One Get One Free',
                'Flash Sale - 50% Off',
                'Weekend Special Discount',
                'New Customer Offer'
            ]),
            'products_services' => $this->faker->randomElement([
                'Electronics, Gadgets, Accessories',
                'Clothing, Fashion, Apparel',
                'Food, Beverages, Snacks',
                'Books, Stationery, Office Supplies',
                'Home, Garden, Furniture'
            ]),
            'discount_type' => $this->faker->randomElement(['percentage', 'fixed']),
            'discount_value' => $this->faker->randomFloat(2, 5, 50),
            'min_amount' => $this->faker->randomFloat(2, 50, 500),
            'currency' => 'INR',
            'start_date' => $startDate,
            'end_date' => $endDate,
            'is_active' => true,
            'is_template' => false,
            'terms_conditions' => $this->faker->sentence(15),
            'max_uses' => $this->faker->randomElement([null, 50, 100, 200]),
            'used_count' => 0,
            'latitude' => $this->faker->latitude(40.0, 41.0),
            'longitude' => $this->faker->longitude(-75.0, -73.0),
            'radius_km' => $this->faker->randomElement([5, 10, 15, 20]),
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'start_date' => now()->subDay(),
            'end_date' => now()->addDays(30),
        ]);
    }

    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'start_date' => now()->subDays(30),
            'end_date' => now()->subDay(),
        ]);
    }
}
