<?php

namespace App\Http\Controllers;

use App\Models\Shopkeeper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ShopkeeperController extends Controller
{
    public function store(Request $request)
    {
        $validated = $request->validate([
            'shop_name' => 'required|string|max:255',
            'business_license' => 'required|string|max:255',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:15',
            'business_type' => 'nullable|string|max:100',
            'description' => 'nullable|string|max:1000',
        ]);

        $user = Auth::user();

        // Check if shopkeeper profile already exists
        if ($user->shopkeeper) {
            return response()->json(['error' => 'Shopkeeper profile already exists'], 400);
        }

        // Check if user has completed phone verification
        if (!$user->phone_verified) {
            return response()->json(['error' => 'Phone verification required'], 403);
        }

        $shopkeeper = Shopkeeper::create([
            'user_id' => $user->id,
            'shop_name' => $validated['shop_name'],
            'business_license' => $validated['business_license'],
            'latitude' => $validated['latitude'],
            'longitude' => $validated['longitude'],
            'address' => $validated['address'] ?? null,
            'phone' => $validated['phone'] ?? $user->phone,
            'business_type' => $validated['business_type'] ?? null,
            'description' => $validated['description'] ?? null,
        ]);

        // Update user registration step
        $user->update(['registration_step' => 'completed']);

        // Create free subscription for new shopkeeper
        \App\Models\Subscription::createFromPlan($user->id, 'free');

        return response()->json([
            'message' => 'Shopkeeper profile created successfully',
            'shopkeeper' => $shopkeeper->load('user'),
            'registration_step' => 'completed'
        ], 201);
    }

    public function update(Request $request, $id)
    {
        $shopkeeper = Shopkeeper::findOrFail($id);

        $validated = $request->validate([
            'shop_name' => 'sometimes|string',
            'business_license' => 'sometimes|string',
            'latitude' => 'sometimes|numeric',
            'longitude' => 'sometimes|numeric',
        ]);

        $shopkeeper->update($validated);

        return response()->json(['message' => 'Shopkeeper details updated successfully', 'shopkeeper' => $shopkeeper], 200);
    }
}
