<?php

namespace Database\Factories;

use App\Models\Shopkeeper;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ShopkeeperFactory extends Factory
{
    protected $model = Shopkeeper::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'shop_name' => $this->faker->company,
            'business_license' => 'BL' . $this->faker->randomNumber(6),
            'latitude' => $this->faker->latitude(40.0, 41.0),
            'longitude' => $this->faker->longitude(-75.0, -73.0),
            'address' => $this->faker->address,
            'phone' => $this->faker->phoneNumber,
            'business_type' => $this->faker->randomElement(['Electronics', 'Clothing', 'Food', 'Services', 'Retail']),
            'description' => $this->faker->sentence(10),
        ];
    }
}
