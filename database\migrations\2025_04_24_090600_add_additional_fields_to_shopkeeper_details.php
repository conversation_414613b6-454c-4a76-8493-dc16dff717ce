<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shopkeeper_details', function (Blueprint $table) {
            $table->string('address')->nullable()->after('longitude');
            $table->string('phone')->nullable()->after('address');
            $table->string('business_type')->nullable()->after('phone');
            $table->text('description')->nullable()->after('business_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shopkeeper_details', function (Blueprint $table) {
            $table->dropColumn(['address', 'phone', 'business_type', 'description']);
        });
    }
};
