<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offer_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shopkeeper_id')->constrained('shopkeeper_details')->onDelete('cascade');
            $table->string('template_name');
            $table->string('business_name');
            $table->string('offer_name');
            $table->text('products_services');
            $table->enum('discount_type', ['percentage', 'fixed'])->default('percentage');
            $table->decimal('discount_value', 8, 2);
            $table->decimal('min_amount', 10, 2)->nullable();
            $table->string('currency', 3)->default('INR');
            $table->text('terms_conditions')->nullable();
            $table->integer('max_uses')->nullable();
            $table->integer('radius_km')->default(5);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offer_templates');
    }
};
