<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ShopkeeperController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\OfferController;
use App\Http\Controllers\OfferTemplateController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\NotificationController;

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/verify-otp', [AuthController::class, 'verifyOtp']);
Route::post('/send-phone-otp', [AuthController::class, 'sendPhoneOtp']);
Route::post('/verify-phone-otp', [AuthController::class, 'verifyPhoneOtp']);
Route::post('/resend-phone-otp', [AuthController::class, 'resendPhoneOtp']);
Route::post('/login', [AuthController::class, 'login']);

// Subscription plans (public)
Route::get('/subscription/plans', [SubscriptionController::class, 'plans']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::get('/validate-user', [AuthController::class, 'getProfile']);
    Route::post('/logout', [AuthController::class, 'logout']);

    // Customer routes
    Route::middleware('role:customer')->group(function () {
        Route::post('/customer/details', [CustomerController::class, 'store']);
        Route::put('/customer/details/{id}', [CustomerController::class, 'update']);

        // Location and offers for customers
        Route::get('/offers/nearby', [OfferController::class, 'nearby']);
        Route::post('/offers/{id}/interact', [OfferController::class, 'interact']);
        Route::get('/location/nearby-shopkeepers', [LocationController::class, 'nearbyShopkeepers']);
        Route::get('/location/nearby-offers', [LocationController::class, 'nearbyOffers']);
    });

    // Shopkeeper routes
    Route::middleware('role:shop-keeper')->group(function () {
        Route::post('/shopkeeper/details', [ShopkeeperController::class, 'store']);
        Route::put('/shopkeeper/details/{id}', [ShopkeeperController::class, 'update']);

        // Offer management
        Route::apiResource('offers', OfferController::class);
        Route::get('/offers/{id}/analytics', [OfferController::class, 'analytics']);

        // Offer templates
        Route::apiResource('offer-templates', OfferTemplateController::class);
        Route::post('/offer-templates/{id}/create-offer', [OfferTemplateController::class, 'createOffer']);

        // Location for shopkeepers
        Route::get('/location/nearby-customers', [LocationController::class, 'nearbyCustomers']);
    });

    // Common routes for all authenticated users
    Route::get('/location/statistics', [LocationController::class, 'statistics']);
    Route::post('/location/update', [LocationController::class, 'updateLocation']);

    // Subscription management
    Route::get('/subscription/current', [SubscriptionController::class, 'current']);
    Route::get('/subscription/history', [SubscriptionController::class, 'history']);
    Route::post('/subscription/subscribe', [SubscriptionController::class, 'subscribe']);
    Route::post('/subscription/cancel', [SubscriptionController::class, 'cancel']);
    Route::post('/subscription/renew', [SubscriptionController::class, 'renew']);
    Route::post('/subscription/upgrade', [SubscriptionController::class, 'upgrade']);
    Route::get('/subscription/check-status', [SubscriptionController::class, 'checkStatus']);

    // Notifications
    Route::get('/notifications', [NotificationController::class, 'index']);
    Route::get('/notifications/unread-count', [NotificationController::class, 'unreadCount']);
    Route::post('/notifications/{id}/mark-read', [NotificationController::class, 'markAsRead']);
    Route::post('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
    Route::delete('/notifications/{id}', [NotificationController::class, 'destroy']);
    Route::post('/notifications/send', [NotificationController::class, 'send']);
    Route::get('/notifications/statistics', [NotificationController::class, 'statistics']);
});
