<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OfferTemplate extends Model
{
    protected $fillable = [
        'shopkeeper_id',
        'template_name',
        'business_name',
        'offer_name',
        'products_services',
        'discount_type',
        'discount_value',
        'min_amount',
        'currency',
        'terms_conditions',
        'max_uses',
        'radius_km',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'discount_value' => 'decimal:2',
        'min_amount' => 'decimal:2',
    ];

    public function shopkeeper(): BelongsTo
    {
        return $this->belongsTo(Shopkeeper::class);
    }

    // Convert template to offer data
    public function toOfferData(array $additionalData = []): array
    {
        $templateData = [
            'shopkeeper_id' => $this->shopkeeper_id,
            'business_name' => $this->business_name,
            'offer_name' => $this->offer_name,
            'products_services' => $this->products_services,
            'discount_type' => $this->discount_type,
            'discount_value' => $this->discount_value,
            'min_amount' => $this->min_amount,
            'currency' => $this->currency,
            'terms_conditions' => $this->terms_conditions,
            'max_uses' => $this->max_uses,
            'radius_km' => $this->radius_km,
        ];

        return array_merge($templateData, $additionalData);
    }

    // Get formatted discount text
    public function getDiscountTextAttribute(): string
    {
        if ($this->discount_type === 'percentage') {
            return $this->discount_value . '% off';
        }
        return $this->currency . ' ' . $this->discount_value . ' off';
    }
}
