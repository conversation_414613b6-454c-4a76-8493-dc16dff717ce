<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SubscriptionController extends Controller
{
    // Get available subscription plans
    public function plans()
    {
        $plans = [
            'free' => Subscription::getPlanDetails('free'),
            'monthly_59' => Subscription::getPlanDetails('monthly_59'),
            'monthly_122' => Subscription::getPlanDetails('monthly_122'),
            'monthly_167' => Subscription::getPlanDetails('monthly_167'),
        ];

        return response()->json($plans);
    }

    // Get user's current subscription
    public function current()
    {
        $user = Auth::user();
        $subscription = $user->activeSubscription();

        if (!$subscription) {
            return response()->json(['message' => 'No active subscription'], 404);
        }

        return response()->json($subscription);
    }

    // Get user's subscription history
    public function history()
    {
        $user = Auth::user();
        $subscriptions = $user->subscriptions()
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($subscriptions);
    }

    // Subscribe to a plan
    public function subscribe(Request $request)
    {
        $validated = $request->validate([
            'plan_type' => 'required|in:free,monthly_59,monthly_122,monthly_167',
            'payment_method' => 'required_unless:plan_type,free|string',
            'transaction_id' => 'required_unless:plan_type,free|string',
        ]);

        $user = Auth::user();

        // Check if user already has an active subscription
        if ($user->hasActiveSubscription()) {
            return response()->json(['error' => 'User already has an active subscription'], 400);
        }

        // For paid plans, validate payment (this would integrate with payment gateway)
        if ($validated['plan_type'] !== 'free') {
            // Here you would validate the payment with your payment gateway
            // For now, we'll assume the payment is valid
            $paymentData = [
                'payment_method' => $validated['payment_method'],
                'transaction_id' => $validated['transaction_id'],
            ];
        } else {
            $paymentData = [];
        }

        $subscription = Subscription::createFromPlan(
            $user->id,
            $validated['plan_type'],
            $paymentData
        );

        // For free plan, activate immediately
        if ($validated['plan_type'] === 'free') {
            $subscription->update(['status' => 'active']);
        }

        return response()->json([
            'message' => 'Subscription created successfully',
            'subscription' => $subscription,
        ], 201);
    }

    // Cancel subscription
    public function cancel()
    {
        $user = Auth::user();
        $subscription = $user->activeSubscription();

        if (!$subscription) {
            return response()->json(['error' => 'No active subscription to cancel'], 404);
        }

        $subscription->update([
            'status' => 'cancelled',
            'is_active' => false,
            'auto_renew' => false,
        ]);

        return response()->json(['message' => 'Subscription cancelled successfully']);
    }

    // Renew subscription
    public function renew(Request $request)
    {
        $validated = $request->validate([
            'payment_method' => 'required|string',
            'transaction_id' => 'required|string',
        ]);

        $user = Auth::user();
        $currentSubscription = $user->subscriptions()
            ->where('status', 'expired')
            ->orderBy('end_date', 'desc')
            ->first();

        if (!$currentSubscription) {
            return response()->json(['error' => 'No subscription to renew'], 404);
        }

        // Create new subscription with same plan
        $newSubscription = Subscription::createFromPlan(
            $user->id,
            $currentSubscription->plan_type,
            $validated
        );

        return response()->json([
            'message' => 'Subscription renewed successfully',
            'subscription' => $newSubscription,
        ], 201);
    }

    // Upgrade subscription
    public function upgrade(Request $request)
    {
        $validated = $request->validate([
            'new_plan_type' => 'required|in:monthly_59,monthly_122,monthly_167',
            'payment_method' => 'required|string',
            'transaction_id' => 'required|string',
        ]);

        $user = Auth::user();
        $currentSubscription = $user->activeSubscription();

        if (!$currentSubscription) {
            return response()->json(['error' => 'No active subscription to upgrade'], 404);
        }

        // Calculate prorated amount (simplified - in real app you'd calculate exact proration)
        $currentPlan = Subscription::getPlanDetails($currentSubscription->plan_type);
        $newPlan = Subscription::getPlanDetails($validated['new_plan_type']);

        if ($newPlan['price'] <= $currentPlan['price']) {
            return response()->json(['error' => 'Cannot downgrade to a lower plan'], 400);
        }

        // Cancel current subscription
        $currentSubscription->update([
            'status' => 'cancelled',
            'is_active' => false,
        ]);

        // Create new subscription
        $newSubscription = Subscription::createFromPlan(
            $user->id,
            $validated['new_plan_type'],
            [
                'payment_method' => $validated['payment_method'],
                'transaction_id' => $validated['transaction_id'],
            ]
        );

        $newSubscription->update(['status' => 'active']);

        return response()->json([
            'message' => 'Subscription upgraded successfully',
            'subscription' => $newSubscription,
        ], 201);
    }

    // Check subscription status and update expired ones
    public function checkStatus()
    {
        $user = Auth::user();
        $subscription = $user->activeSubscription();

        if ($subscription && $subscription->isExpired()) {
            $subscription->update([
                'status' => 'expired',
                'is_active' => false,
            ]);
            
            return response()->json([
                'status' => 'expired',
                'message' => 'Subscription has expired',
            ]);
        }

        return response()->json([
            'status' => $subscription ? 'active' : 'none',
            'subscription' => $subscription,
        ]);
    }
}
