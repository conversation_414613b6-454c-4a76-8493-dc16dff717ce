<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    protected $fillable = [
        'sender_id',
        'receiver_id',
        'offer_id',
        'title',
        'message',
        'type',
        'is_read',
        'sent_at',
        'metadata',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'sent_at' => 'datetime',
        'metadata' => 'array',
    ];

    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    public function offer(): BelongsTo
    {
        return $this->belongsTo(Offer::class);
    }

    // Scope for unread notifications
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    // Mark as read
    public function markAsRead(): bool
    {
        return $this->update(['is_read' => true]);
    }

    // Create offer notification
    public static function createOfferNotification($senderId, $receiverId, $offerId, $customMessage = null): self
    {
        $offer = Offer::find($offerId);
        
        return self::create([
            'sender_id' => $senderId,
            'receiver_id' => $receiverId,
            'offer_id' => $offerId,
            'title' => 'New Offer Available!',
            'message' => $customMessage ?? "Check out this amazing offer: {$offer->offer_name}",
            'type' => 'offer',
            'sent_at' => now(),
            'metadata' => [
                'offer_name' => $offer->offer_name,
                'discount_text' => $offer->discount_text,
                'business_name' => $offer->business_name,
            ],
        ]);
    }

    // Send bulk notifications to nearby customers
    public static function sendToNearbyCustomers($offerId, $latitude, $longitude, $radiusKm = 5): int
    {
        $offer = Offer::with('shopkeeper.user')->find($offerId);
        if (!$offer) return 0;

        // Get nearby customers
        $nearbyCustomers = Customer::selectRaw("
            *, 
            (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
        ", [$latitude, $longitude, $latitude])
        ->having('distance', '<=', $radiusKm)
        ->with('user')
        ->get();

        $notificationCount = 0;
        foreach ($nearbyCustomers as $customer) {
            self::createOfferNotification(
                $offer->shopkeeper->user_id,
                $customer->user_id,
                $offerId
            );
            $notificationCount++;
        }

        return $notificationCount;
    }
}
