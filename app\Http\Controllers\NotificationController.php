<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    // Get user's notifications
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $notifications = $user->receivedNotifications()
            ->with(['sender', 'offer'])
            ->when($request->type, function ($query, $type) {
                return $query->where('type', $type);
            })
            ->when($request->unread_only, function ($query) {
                return $query->unread();
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json($notifications);
    }

    // Get unread notifications count
    public function unreadCount()
    {
        $user = Auth::user();
        $count = $user->receivedNotifications()->unread()->count();

        return response()->json(['unread_count' => $count]);
    }

    // Mark notification as read
    public function markAsRead($id)
    {
        $user = Auth::user();
        $notification = $user->receivedNotifications()->findOrFail($id);
        
        $notification->markAsRead();

        return response()->json(['message' => 'Notification marked as read']);
    }

    // Mark all notifications as read
    public function markAllAsRead()
    {
        $user = Auth::user();
        
        $user->receivedNotifications()
            ->unread()
            ->update(['is_read' => true]);

        return response()->json(['message' => 'All notifications marked as read']);
    }

    // Delete notification
    public function destroy($id)
    {
        $user = Auth::user();
        $notification = $user->receivedNotifications()->findOrFail($id);
        
        $notification->delete();

        return response()->json(['message' => 'Notification deleted']);
    }

    // Send custom notification (for admin/shopkeeper)
    public function send(Request $request)
    {
        $request->validate([
            'receiver_id' => 'required|exists:users,id',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'type' => 'required|in:offer,subscription,general,promotion',
            'offer_id' => 'nullable|exists:offers,id',
        ]);

        $user = Auth::user();

        $notification = Notification::create([
            'sender_id' => $user->id,
            'receiver_id' => $request->receiver_id,
            'offer_id' => $request->offer_id,
            'title' => $request->title,
            'message' => $request->message,
            'type' => $request->type,
            'sent_at' => now(),
        ]);

        return response()->json([
            'message' => 'Notification sent successfully',
            'notification' => $notification,
        ], 201);
    }

    // Get notification statistics
    public function statistics()
    {
        $user = Auth::user();

        $stats = [
            'total_notifications' => $user->receivedNotifications()->count(),
            'unread_notifications' => $user->receivedNotifications()->unread()->count(),
            'offer_notifications' => $user->receivedNotifications()->where('type', 'offer')->count(),
            'recent_notifications' => $user->receivedNotifications()
                ->where('created_at', '>=', now()->subDays(7))
                ->count(),
        ];

        return response()->json($stats);
    }
}
