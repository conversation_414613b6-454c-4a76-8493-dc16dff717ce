<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Shopkeeper extends Model
{
    protected $table = 'shopkeeper_details';

    protected $fillable = [
        'user_id',
        'shop_name',
        'business_license',
        'latitude',
        'longitude',
        'address',
        'phone',
        'business_type',
        'description',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function offers()
    {
        return $this->hasMany(Offer::class);
    }

    public function offerTemplates()
    {
        return $this->hasMany(OfferTemplate::class);
    }

    // Get active offers
    public function activeOffers()
    {
        return $this->offers()->active();
    }

    // Get nearby customers
    public function nearbyCustomers($radiusKm = 10)
    {
        return Customer::selectRaw("
            *,
            (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
        ", [$this->latitude, $this->longitude, $this->latitude])
            ->having('distance', '<=', $radiusKm)
            ->orderBy('distance');
    }
}
