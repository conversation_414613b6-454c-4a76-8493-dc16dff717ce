<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Subscription extends Model
{
    protected $fillable = [
        'user_id',
        'plan_type',
        'plan_name',
        'price',
        'currency',
        'start_date',
        'end_date',
        'is_active',
        'auto_renew',
        'payment_method',
        'transaction_id',
        'status',
        'features',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
        'auto_renew' => 'boolean',
        'price' => 'decimal:2',
        'features' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scope for active subscriptions
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('status', 'active')
                    ->where('end_date', '>=', now());
    }

    // Check if subscription is currently active
    public function isCurrentlyActive(): bool
    {
        return $this->is_active && 
               $this->status === 'active' && 
               $this->end_date >= now();
    }

    // Check if subscription is expired
    public function isExpired(): bool
    {
        return $this->end_date < now();
    }

    // Get days remaining
    public function getDaysRemainingAttribute(): int
    {
        if ($this->isExpired()) {
            return 0;
        }
        return now()->diffInDays($this->end_date);
    }

    // Get subscription plan details
    public static function getPlanDetails($planType): array
    {
        $plans = [
            'free' => [
                'name' => 'Free for 12 hrs',
                'price' => 0,
                'duration_hours' => 12,
                'features' => ['Basic offer creation', 'Limited reach']
            ],
            'monthly_59' => [
                'name' => 'LET ME TRY PLAN MONTHLY',
                'price' => 59,
                'duration_days' => 30,
                'features' => ['Unlimited offers', 'Extended reach', 'Analytics']
            ],
            'monthly_122' => [
                'name' => 'OK GIVE ME THE GOOD',
                'price' => 122,
                'duration_days' => 30,
                'features' => ['All features', 'Priority support', 'Advanced analytics']
            ],
            'monthly_167' => [
                'name' => 'OK GIVE ME THE BEST',
                'price' => 167,
                'duration_days' => 30,
                'features' => ['Premium features', '24/7 support', 'Custom branding']
            ],
        ];

        return $plans[$planType] ?? [];
    }

    // Create subscription from plan
    public static function createFromPlan($userId, $planType, $paymentData = []): self
    {
        $planDetails = self::getPlanDetails($planType);
        
        $startDate = now();
        $endDate = $planType === 'free' 
            ? $startDate->copy()->addHours($planDetails['duration_hours'] ?? 12)
            : $startDate->copy()->addDays($planDetails['duration_days'] ?? 30);

        return self::create([
            'user_id' => $userId,
            'plan_type' => $planType,
            'plan_name' => $planDetails['name'],
            'price' => $planDetails['price'],
            'currency' => 'INR',
            'start_date' => $startDate,
            'end_date' => $endDate,
            'is_active' => true,
            'status' => $planType === 'free' ? 'active' : 'pending',
            'features' => $planDetails['features'],
            'payment_method' => $paymentData['payment_method'] ?? null,
            'transaction_id' => $paymentData['transaction_id'] ?? null,
        ]);
    }
}
